["memory_test.py::test_default_json_reader", "memory_test.py::test_streaming_json_reader", "test_comprehensive_suite.py::TestAdvancedScenarios::test_deeply_nested_structures", "test_comprehensive_suite.py::TestAdvancedScenarios::test_mixed_data_types", "test_comprehensive_suite.py::TestAdvancedScenarios::test_special_json_values", "test_comprehensive_suite.py::TestAdvancedScenarios::test_wide_objects", "test_comprehensive_suite.py::TestErrorHandling::test_empty_and_whitespace_files", "test_comprehensive_suite.py::TestErrorHandling::test_file_not_found", "test_comprehensive_suite.py::TestErrorHandling::test_malformed_json", "test_comprehensive_suite.py::TestGenericJSONSupport::test_array_structures", "test_comprehensive_suite.py::TestGenericJSONSupport::test_primitive_values", "test_comprehensive_suite.py::TestGenericJSONSupport::test_simple_objects", "test_comprehensive_suite.py::TestPerformanceAndStress::test_concurrent_access", "test_comprehensive_suite.py::TestPerformanceAndStress::test_medium_sized_files", "test_comprehensive_suite.py::TestResourceManagement::test_file_size_limits", "test_comprehensive_suite.py::TestResourceManagement::test_normal_file_sizes", "test_comprehensive_suite.py::TestUnicodeSupport::test_emojis_and_symbols", "test_comprehensive_suite.py::TestUnicodeSupport::test_escape_sequences", "test_comprehensive_suite.py::TestUnicodeSupport::test_international_characters", "test_performance_comparison.py::TestPerformanceComparison::test_default_json_reader_comparison", "test_performance_comparison.py::TestPerformanceComparison::test_different_json_structures", "test_performance_comparison.py::TestPerformanceComparison::test_memory_efficiency_claims", "test_performance_comparison.py::TestPerformanceComparison::test_scalability_with_file_size", "test_streaming_json_reader.py::TestStreamingJsonReader::test_basic_json_reading", "test_streaming_json_reader.py::TestStreamingJsonReader::test_column_types", "test_streaming_json_reader.py::TestStreamingJsonReader::test_empty_results", "test_streaming_json_reader.py::TestStreamingJsonReader::test_error_handling", "test_streaming_json_reader.py::TestStreamingJsonReader::test_extension_loads", "test_streaming_json_reader.py::TestStreamingJsonReader::test_large_file_handling", "test_streaming_json_reader.py::TestStreamingJsonReader::test_memory_efficiency_vs_default_reader", "test_streaming_json_reader.py::TestStreamingJsonReader::test_multiply_nested_path_support", "test_streaming_json_reader.py::TestStreamingJsonReader::test_path_inference", "tests/integration/test_streaming_json_reader.py::TestStreamingJsonReader::test_basic_json_reading", "tests/integration/test_streaming_json_reader.py::TestStreamingJsonReader::test_column_types", "tests/integration/test_streaming_json_reader.py::TestStreamingJsonReader::test_empty_results", "tests/integration/test_streaming_json_reader.py::TestStreamingJsonReader::test_error_handling", "tests/integration/test_streaming_json_reader.py::TestStreamingJsonReader::test_extension_loads", "tests/integration/test_streaming_json_reader.py::TestStreamingJsonReader::test_large_file_handling", "tests/integration/test_streaming_json_reader.py::TestStreamingJsonReader::test_memory_efficiency_vs_default_reader", "tests/integration/test_streaming_json_reader.py::TestStreamingJsonReader::test_multiply_nested_path_support", "tests/integration/test_streaming_json_reader.py::TestStreamingJsonReader::test_path_inference", "tests/test_deep_nesting_stress.py::TestDeepNestingStress::test_extreme_nesting_25_levels", "tests/test_deep_nesting_stress.py::TestDeepNestingStress::test_mixed_deep_nesting_with_arrays", "tests/test_deep_nesting_stress.py::TestDeepNestingStress::test_nesting_depth_10_levels", "tests/test_deep_nesting_stress.py::TestDeepNestingStress::test_nesting_depth_15_levels", "tests/test_deep_nesting_stress.py::TestDeepNestingStress::test_nesting_depth_5_levels", "tests/test_deep_nesting_stress.py::TestDeepNestingStress::test_schema_consistency_deep_nesting", "tests/test_deep_nesting_stress.py::TestDeepNestingStress::test_verify_no_varchar_fallbacks", "tests/test_json.py::TestStreamingJsonReader::test_2d_array_numbers", "tests/test_json.py::TestStreamingJsonReader::test_3d_array_numbers", "tests/test_json.py::TestStreamingJsonReader::test_4d_array_numbers", "tests/test_json.py::TestStreamingJsonReader::test_array_basic_elements", "tests/test_json.py::TestStreamingJsonReader::test_array_flattening", "tests/test_json.py::TestStreamingJsonReader::test_array_of_structs", "tests/test_json.py::TestStreamingJsonReader::test_boolean_values", "tests/test_json.py::TestStreamingJsonReader::test_deep_nested_structures", "tests/test_json.py::TestStreamingJsonReader::test_empty_file_handling", "tests/test_json.py::TestStreamingJsonReader::test_empty_object", "tests/test_json.py::TestStreamingJsonReader::test_irregular_2d_arrays", "tests/test_json.py::TestStreamingJsonReader::test_large_json_memory_efficiency", "tests/test_json.py::TestStreamingJsonReader::test_malformed_json_error", "tests/test_json.py::TestStreamingJsonReader::test_mixed_depth_arrays", "tests/test_json.py::TestStreamingJsonReader::test_nested_array_access", "tests/test_json.py::TestStreamingJsonReader::test_nested_struct_access", "tests/test_json.py::TestStreamingJsonReader::test_nonexistent_file_error", "tests/test_json.py::TestStreamingJsonReader::test_numeric_precision", "tests/test_json.py::TestStreamingJsonReader::test_projection_pushdown_single_field", "tests/test_json.py::TestStreamingJsonReader::test_projection_pushdown_struct_field", "tests/test_json.py::TestStreamingJsonReader::test_simple_object_basic_types", "tests/test_json.py::TestStreamingJsonReader::test_streaming_large_array", "tests/test_json.py::TestStreamingJsonReader::test_struct_field_access", "tests/test_json.py::TestStreamingJsonReader::test_varying_row_lengths", "tests/test_recursive_schema.py::test_array_of_primitives", "tests/test_recursive_schema.py::test_basic_array_unnesting", "tests/test_recursive_schema.py::test_deeply_nested_structure", "tests/test_recursive_schema.py::test_mixed_types_and_arrays", "tests/test_recursive_schema.py::test_multiple_arrays", "tests/test_recursive_schema.py::test_nested_object_unnesting", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_array_basic_elements", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_array_flattening", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_array_of_structs", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_boolean_values", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_deep_nested_structures", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_empty_file_handling", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_empty_object", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_large_json_memory_efficiency", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_malformed_json_error", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_nested_array_access", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_nested_struct_access", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_nonexistent_file_error", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_numeric_precision", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_projection_pushdown_single_field", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_projection_pushdown_struct_field", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_simple_object_basic_types", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_streaming_large_array", "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_struct_field_access"]