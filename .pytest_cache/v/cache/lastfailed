{"tests/integration/test_streaming_json_reader.py::TestStreamingJsonReader::test_multiply_nested_path_support": true, "tests/integration/test_streaming_json_reader.py::TestStreamingJsonReader::test_path_inference": true, "tests/integration/test_streaming_json_reader.py::TestStreamingJsonReader::test_empty_results": true, "memory_test.py::test_default_json_reader": true, "memory_test.py::test_streaming_json_reader": true, "tests/test_recursive_schema.py::test_deeply_nested_structure": true, "tests/test_recursive_schema.py::test_mixed_types_and_arrays": true, "tests/test_recursive_schema.py::test_array_of_primitives": true, "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_simple_object_basic_types": true, "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader::test_struct_field_access": true, "tests/test_streaming_json_reader_comprehensive.py::TestStreamingJsonReader": true, "tests/test_json.py::TestStreamingJsonReader::test_large_json_memory_efficiency": true, "tests/test_json.py::TestStreamingJsonReader::test_nonexistent_file_error": true, "tests/test_json.py::TestStreamingJsonReader::test_empty_file_handling": true, "tests/test_json.py::TestStreamingJsonReader::test_numeric_precision": true, "tests/test_json.py::TestStreamingJsonReader::test_varying_row_lengths": true, "tests/test_json.py::TestStreamingJsonReader::test_4d_array_numbers": true}